<?php
/**
 * Course Linking Handler for Custom Linking Plugin
 * 
 * This file handles AJAX requests to query course-product links
 * and logs the results to debug.log
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Handle AJAX request for course linking
 * 
 * This function processes the course ID, queries the database for linked products,
 * and logs the result to debug.log
 */
function custom_linking_handle_course_request() {
    // Clear previous debug messages
    custom_linking_clear_debug_log();
    
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'custom_linking_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }
    
    // Get course ID from POST data
    $course_id = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    
    if (!$course_id) {
        custom_linking_debug_log('Error: No course ID provided');
        wp_send_json_error(array('message' => 'No course ID provided'));
        return;
    }
    
    // Query the database for linked product
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $product_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d LIMIT 1",
            $course_id
        )
    );
    
    if ($product_id) {
        // Log the successful link
        $message = "Course ID {$course_id} linked to Product ID {$product_id}";
        custom_linking_debug_log($message);

        // Try to add product to WooCommerce cart
        if (function_exists('WC') && WC()->cart) {
            // COURSE CART CONFLICT PREVENTION
            // Before adding individual course product, check if this course is already included in any bundle in cart
            custom_linking_debug_log("Checking for cart conflicts before adding course product {$product_id} for course {$course_id}");

            $conflicting_bundles = array();
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $cart_product_id = $cart_item['product_id'];

                // Check if this cart product is linked to a bundle
                $cart_linked_courses = custom_linking_get_courses_for_product($cart_product_id);
                foreach ($cart_linked_courses as $cart_course_link) {
                    if ($cart_course_link->type === 'bundle') {
                        // Check if this bundle contains our course
                        $bundle_courses = custom_linking_get_bundle_courses_for_conflict_check($cart_course_link->course_id);
                        if (in_array($course_id, $bundle_courses)) {
                            $conflicting_bundles[] = array(
                                'product_id' => $cart_product_id,
                                'bundle_id' => $cart_course_link->course_id,
                                'cart_key' => $cart_item_key
                            );
                            custom_linking_debug_log("Found conflicting bundle {$cart_course_link->course_id} (product {$cart_product_id}) that already contains course {$course_id}");
                        }
                    }
                }
            }

            // If course is already in a bundle, don't add individual course
            if (!empty($conflicting_bundles)) {
                custom_linking_debug_log("Course {$course_id} is already included in " . count($conflicting_bundles) . " bundle(s) in cart. Not adding individual course.");

                // Redirect to cart since bundle already contains this course
                $cart_url = function_exists('wc_get_cart_url') ? wc_get_cart_url() : '';
                wp_send_json_success(array(
                    'message' => "Course is already included in a bundle in your cart",
                    'course_id' => $course_id,
                    'product_id' => $product_id,
                    'cart_added' => false,
                    'cart_url' => $cart_url,
                    'redirect_to' => 'cart',
                    'conflict_reason' => 'bundle_contains_course'
                ));
                return;
            }

            // Add product to cart
            $cart_item_key = WC()->cart->add_to_cart($product_id);

            if ($cart_item_key) {
                // Successfully added to cart
                $cart_url = function_exists('wc_get_cart_url') ? wc_get_cart_url() : '';
                custom_linking_debug_log("Product ID {$product_id} successfully added to cart");

                wp_send_json_success(array(
                    'message' => $message,
                    'course_id' => $course_id,
                    'product_id' => $product_id,
                    'cart_added' => true,
                    'cart_url' => $cart_url,
                    'redirect_to' => 'cart'
                ));
            } else {
                // Failed to add to cart, redirect to product page
                $product_url = get_permalink($product_id);
                custom_linking_debug_log("Failed to add Product ID {$product_id} to cart, redirecting to product page");

                wp_send_json_success(array(
                    'message' => $message,
                    'course_id' => $course_id,
                    'product_id' => $product_id,
                    'cart_added' => false,
                    'product_url' => $product_url,
                    'redirect_to' => 'product'
                ));
            }
        } else {
            // WooCommerce not available
            custom_linking_debug_log("WooCommerce not available, cannot add product to cart");

            wp_send_json_error(array(
                'message' => 'WooCommerce is not available',
                'course_id' => $course_id,
                'product_id' => $product_id
            ));
        }
    } else {
        // Log that no link was found
        $message = "Course ID {$course_id} has no linked product";
        custom_linking_debug_log($message);

        wp_send_json_success(array(
            'message' => $message,
            'course_id' => $course_id,
            'product_id' => null,
            'cart_added' => false,
            'redirect_to' => 'none'
        ));
    }
}

// Note: custom_linking_get_bundle_courses_for_conflict_check function is defined in frontend/bundleLinking.php
// to avoid duplicate function definition

// Note: custom_linking_get_courses_for_product function is defined in frontend/order-completion.php
// to avoid duplicate function definition

// Register AJAX handlers
add_action('wp_ajax_custom_linking_course_request', 'custom_linking_handle_course_request');
add_action('wp_ajax_nopriv_custom_linking_course_request', 'custom_linking_handle_course_request');
