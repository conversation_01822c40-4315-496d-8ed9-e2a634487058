/**
 * Custom Linking Plugin Admin JavaScript
 * 
 * This file handles client-side functionality for the Custom Linking Plugin admin interface
 * It manages form validation, AJAX interactions, and UI feedback
 */

jQuery(document).ready(function($) {
    
    /**
     * Form validation logic
     * Validates form inputs before submission to ensure required fields are completed
     * Prevents submission if validation fails and shows appropriate error messages
     */
    $('.custom-linking-admin-form form').on('submit', function(e) {
        var courseId = $('#course_id').val();
        var productId = $('#product_id').val();
        
        // Check if required fields are filled
        if (!courseId || !productId) {
            e.preventDefault();
            
            // Add error highlighting to empty fields
            if (!courseId) {
                $('#course_id').addClass('error');
            }
            
            if (!productId) {
                $('#product_id').addClass('error');
            }
            
            // Show error message
            alert(custom_linking_data.messages.error);
            return false;
        }
        
        return true;
    });
    
    /**
     * Form field interaction
     * Clears error styling when users interact with previously invalid fields
     */
    $('#course_id, #product_id').on('change', function() {
        $(this).removeClass('error');
    });
    
    /**
     * Notice dismissal
     * Allows WordPress admin notices to be dismissed by clicking the dismiss button
     */
    $(document).on('click', '.notice-dismiss', function() {
        $(this).parent().fadeOut(300, function() {
            $(this).remove();
        });
    });
    
    /**
     * Delete confirmation
     * Adds a confirmation dialog when deleting links to prevent accidental deletion
     */
    $('.custom-linking-admin-list').on('click', '.delete-link', function(e) {
        if (!confirm(custom_linking_data.messages.delete_confirm)) {
            e.preventDefault();
            return false;
        }
        return true;
    });
});
