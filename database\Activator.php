<?php
/**
 * Handles plugin activation tasks
 * 
 * This file creates the database table needed for storing course-product links
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Activator class for Custom Linking Plugin
 * 
 * Only handles creating the database table on activation
 */
class Custom_Linking_Activator {
    /**
     * Create necessary database tables on plugin activation
     * 
     * This method creates the required database table for the plugin
     * and logs any errors during the process
     */
    public static function activate() {
        try {
            global $wpdb;
            $table_name = $wpdb->prefix . 'linking_table';
            

            
            // Get proper charset
            $charset_collate = $wpdb->get_charset_collate();
            
            // Check if table already exists
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
                $sql = "CREATE TABLE {$table_name} (
                    id mediumint(9) NOT NULL AUTO_INCREMENT,
                    course_id bigint(20) NOT NULL,
                    product_id bigint(20) NOT NULL,
                    type enum('course','bundle') NOT NULL DEFAULT 'course',
                    PRIMARY KEY (id),
                    KEY course_id (course_id),
                    KEY product_id (product_id)
                ) {$charset_collate};";
                
                // Include WordPress database upgrade functions
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
                
                // Attempt to create the table
                $result = dbDelta($sql);

            } else {

            }
            

            return true;
        } catch (Exception $e) {

            return false;
        }
    }
    

}