<?php
/**
 * Common utility functions for Custom Linking Plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Debug logger for the plugin
 * 
 * This function logs messages to the WordPress debug log
 * when debugging is enabled
 * 
 * @param mixed $message The message to log
 */
function custom_linking_log($message) {
    if (WP_DEBUG === true) {
        if (is_array($message) || is_object($message)) {
            error_log(print_r($message, true));
        } else {
            error_log($message);
        }
    }
}

/**
 * Check if WooCommerce is active
 * 
 * @return bool True if WooCommerce is active
 */
function custom_linking_is_woocommerce_active() {
    return in_array(
        'woocommerce/woocommerce.php', 
        apply_filters('active_plugins', get_option('active_plugins'))
    );
}

/**
 * Check if MasterStudy LMS is active
 * 
 * @return bool True if MasterStudy LMS is active
 */
function custom_linking_is_masterstudy_active() {
    return in_array(
        'masterstudy-lms-learning-management-system/masterstudy-lms-learning-management-system.php', 
        apply_filters('active_plugins', get_option('active_plugins'))
    );
}

/**
 * Sanitize and validate type field
 * 
 * @param string $type The type to sanitize
 * @return string Sanitized type
 */
function custom_linking_sanitize_type($type) {
    $type = sanitize_text_field($type);
    return in_array($type, array('course', 'bundle')) ? $type : 'course';
}