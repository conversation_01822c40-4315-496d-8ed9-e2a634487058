<?php
/**
 * Bundle Linking Handler for Custom Linking Plugin
 * 
 * This file handles AJAX requests to process bundle purchases
 * It queries product links for bundles and redirects to WooCommerce cart
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Process bundle purchase AJAX request
 * 
 * This function handles the AJAX request when a user clicks the "Purchase Bundle" button
 * It finds linked products in our custom table and redirects to WooCommerce cart
 */
function custom_linking_process_bundle() {
    /**
     * Verify the security nonce to prevent CSRF attacks
     * This ensures the request came from our site and not a malicious source
     */
    check_ajax_referer('custom_linking_nonce', 'security');
    
    /**
     * Get and validate the bundle ID from the AJAX request
     * We use intval to ensure it's a valid integer value
     */
    $bundle_id = isset($_POST['bundle_id']) ? intval($_POST['bundle_id']) : 0;
    if (!$bundle_id) {
        wp_send_json_error(array('error' => 'Invalid bundle ID'));
        return;
    }
    
    /**
     * Log the bundle ID for debugging purposes
     * This helps troubleshoot issues in the integration
     */
    custom_linking_debug_log('Processing bundle ID: ' . $bundle_id);
    
    /**
     * Get linked product from our custom table
     * This queries the linking_table for a product linked to this bundle
     */
    $linked_product = custom_linking_get_linked_product_by_bundle($bundle_id);
    
    /**
     * Process the result based on whether a linked product was found
     * If a product is linked, we add it to the cart and redirect
     * If no product is linked, we return an error message
     */
    if ($linked_product) {
        custom_linking_debug_log('Found linked product ID: ' . $linked_product . ' for bundle: ' . $bundle_id);
        
        /**
         * Check if WooCommerce is active and add the product to cart
         * This requires the WooCommerce plugin to be active and available
         */
        if (function_exists('WC') && WC()->cart) {
            // Make sure WooCommerce cart is properly initialized
            if (!did_action('woocommerce_init')) {
                WC()->frontend_includes();
                WC()->cart = new WC_Cart();
                WC()->session = new WC_Session_Handler();
                WC()->session->init();
                WC()->customer = new WC_Customer(get_current_user_id(), true);
                WC()->cart->get_cart();
                custom_linking_debug_log('WooCommerce cart initialized');
            }
            
            // Verify the product exists
            $product = wc_get_product($linked_product);
            if (!$product) {
                custom_linking_debug_log('Error: Product ID ' . $linked_product . ' does not exist');
                wp_send_json_error(array('error' => 'Product does not exist'));
                return;
            }
            
            // Check if product is purchasable
            $is_purchasable = $product->is_purchasable();
            custom_linking_debug_log('Product ID ' . $linked_product . ' purchasable status: ' . ($is_purchasable ? 'true' : 'false'));
            
            // Diagnose why product might not be purchasable
            if (!$is_purchasable) {
                custom_linking_debug_log('Checking why Product ID ' . $linked_product . ' is not purchasable');
                
                // Check product status
                $product_status = get_post_status($linked_product);
                custom_linking_debug_log('Product status: ' . $product_status);
                if ($product_status !== 'publish') {
                    custom_linking_debug_log('Setting product status to publish');
                    wp_update_post(array(
                        'ID' => $linked_product,
                        'post_status' => 'publish'
                    ));
                }
                
                // Check price
                $price = $product->get_price();
                custom_linking_debug_log('Product price: ' . $price);
                if (!$price || $price <= 0) {
                    // Set a default price if none exists
                    $default_price = 10.00; // $10 default price
                    custom_linking_debug_log('Setting default price of ' . $default_price);
                    update_post_meta($linked_product, '_regular_price', $default_price);
                    update_post_meta($linked_product, '_price', $default_price);
                }
                
                // Update product stock status
                if (!$product->is_in_stock()) {
                    custom_linking_debug_log('Product is out of stock, marking as in stock');
                    update_post_meta($linked_product, '_stock_status', 'instock');
                    update_post_meta($linked_product, '_stock', 999); // Set high stock
                    update_post_meta($linked_product, '_manage_stock', 'no'); // Don't manage stock
                }
                
                // Check if product is virtual (might be better for course products)
                $is_virtual = get_post_meta($linked_product, '_virtual', true);
                if ($is_virtual !== 'yes') {
                    custom_linking_debug_log('Setting product as virtual');
                    update_post_meta($linked_product, '_virtual', 'yes');
                }
                
                // Check if sold individually and remove restriction if so
                $sold_individually = get_post_meta($linked_product, '_sold_individually', true);
                if ($sold_individually === 'yes') {
                    custom_linking_debug_log('Removing sold individually restriction');
                    update_post_meta($linked_product, '_sold_individually', 'no');
                }
                
                // Set product as purchasable by manipulating the filter
                add_filter('woocommerce_is_purchasable', function($purchasable, $obj) use ($linked_product) {
                    if ($obj->get_id() == $linked_product) {
                        custom_linking_debug_log('Forcing Product ID ' . $linked_product . ' to be purchasable via filter');
                        return true;
                    }
                    return $purchasable;
                }, 999, 2); // High priority to override other filters
                
                // Refresh the product object after our changes
                $product = wc_get_product($linked_product);
                custom_linking_debug_log('Product purchasable status after fixes: ' . ($product->is_purchasable() ? 'true' : 'false'));
            }
            
            // BUNDLE CART CONFLICT PREVENTION
            // Before adding bundle product, check if any individual courses from this bundle are already in cart
            custom_linking_debug_log("Checking for cart conflicts before adding bundle product {$linked_product}");

            // Get courses that would be included in this bundle
            $bundle_courses = custom_linking_get_bundle_courses_for_conflict_check($bundle_id);
            custom_linking_debug_log("Bundle {$bundle_id} contains courses: " . print_r($bundle_courses, true));

            // Check if any of these courses are already in cart as individual products
            $conflicting_products = array();
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $cart_product_id = $cart_item['product_id'];

                // Check if this cart product is linked to any course in our bundle
                $cart_linked_courses = custom_linking_get_courses_for_product($cart_product_id);
                foreach ($cart_linked_courses as $cart_course_link) {
                    if ($cart_course_link->type === 'course' && in_array($cart_course_link->course_id, $bundle_courses)) {
                        $conflicting_products[] = array(
                            'product_id' => $cart_product_id,
                            'course_id' => $cart_course_link->course_id,
                            'cart_key' => $cart_item_key
                        );
                        custom_linking_debug_log("Found conflicting product {$cart_product_id} for course {$cart_course_link->course_id} in cart");
                    }
                }
            }

            // Remove conflicting individual course products from cart
            if (!empty($conflicting_products)) {
                custom_linking_debug_log("Removing " . count($conflicting_products) . " conflicting products from cart before adding bundle");
                foreach ($conflicting_products as $conflict) {
                    WC()->cart->remove_cart_item($conflict['cart_key']);
                    custom_linking_debug_log("Removed conflicting product {$conflict['product_id']} (course {$conflict['course_id']}) from cart");
                }
            }

            // Add product to cart - specify quantity and other arguments like in course linking
            $cart_item_key = WC()->cart->add_to_cart(
                $linked_product,  // product_id
                1,                 // quantity
                0,                 // variation_id
                array(),           // variation
                array()            // cart_item_data
            );
            
            if ($cart_item_key) {
                custom_linking_debug_log('Successfully added product ID: ' . $linked_product . ' to cart (key: ' . $cart_item_key . ')');
                
                // Send success response with cart URL for redirection
                wp_send_json_success(array(
                    'linked_product' => true,
                    'product_id' => $linked_product,
                    'cart_url' => function_exists('wc_get_cart_url') ? wc_get_cart_url() : ''
                ));
            } else {
                // If cart addition fails, maybe try to redirect to product page
                $product_url = get_permalink($linked_product);
                custom_linking_debug_log('Error: Failed to add product ID: ' . $linked_product . ' to cart, redirecting to product page');
                
                // Still return success but with different redirect
                wp_send_json_success(array(
                    'linked_product' => true,
                    'product_id' => $linked_product, 
                    'cart_added' => false,
                    'product_url' => $product_url,
                    'redirect_to' => 'product'
                ));
            }
        } else {
            custom_linking_debug_log('Error: WooCommerce is not active or cart not available');
            wp_send_json_error(array('error' => 'WooCommerce is not active. Cannot process purchase.'));
        }
    } else {
        /**
         * Handle the case where no linked product was found
         * This could happen if the bundle hasn't been linked to any WooCommerce product
         */
        custom_linking_debug_log('No linked product found for bundle ID: ' . $bundle_id);
        wp_send_json_error(array('error' => 'No WooCommerce product linked to this bundle. Please contact the administrator.'));
    }
}

/**
 * Helper function to get courses in a bundle for conflict checking
 *
 * This function gets the list of course IDs that are included in a bundle
 * Used to check for cart conflicts when adding bundle products
 *
 * @param int $bundle_id The bundle ID to get courses for
 * @return array Array of course IDs in the bundle
 */
function custom_linking_get_bundle_courses_for_conflict_check($bundle_id) {
    $courses = array();

    // For now, use the manual configuration from bundle-completion.php
    // This should be replaced with proper bundle configuration
    if ($bundle_id == 49741) {
        // Bundle 49741 contains course 49599
        $courses = array(49599);
        custom_linking_debug_log("Manual bundle course configuration: Bundle {$bundle_id} contains courses " . print_r($courses, true));
    }

    // Add more bundle configurations as needed
    // if ($bundle_id == ANOTHER_BUNDLE_ID) {
    //     $courses = array(COURSE_ID_1, COURSE_ID_2, COURSE_ID_3);
    // }

    return $courses;
}

/**
 * Helper function to get linked product ID for a bundle
 *
 * This function queries the linking table for products linked to bundle IDs
 * It uses the same table as course linking but with a type of 'bundle'
 *
 * @param int $bundle_id The bundle ID to find linked products for
 * @return int|false The linked product ID or false if none found
 */
function custom_linking_get_linked_product_by_bundle($bundle_id) {
    /**
     * Access the WordPress database to query our custom linking table
     * We use the global $wpdb object for database operations
     */
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    /**
     * Query the database for a product linked to this bundle
     * We use the 'bundle' type to differentiate from course links
     */
    $product_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT product_id FROM $table_name WHERE course_id = %d AND type = %s LIMIT 1",
            $bundle_id,
            'bundle'
        )
    );
    
    /**
     * Log the result for debugging purposes
     * This helps track whether bundles are properly linked
     */
    if ($product_id) {
        custom_linking_debug_log("Found linked product: $product_id for bundle: $bundle_id");
        return (int) $product_id;
    } else {
        custom_linking_debug_log("No linked product found for bundle: $bundle_id");
        return false;
    }
}

// Register AJAX handlers
add_action('wp_ajax_custom_linking_process_bundle', 'custom_linking_process_bundle');
add_action('wp_ajax_nopriv_custom_linking_process_bundle', 'custom_linking_process_bundle');
