<?php
/**
 * <PERSON>les plugin deactivation tasks
 *
 * This file contains simplified deactivation logic
 * that only drops the custom table
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Deactivator class for Custom Linking Plugin
 * 
 * Only handles dropping the database table on deactivation
 * All other cleanup has been removed for simplicity
 */
class Custom_Linking_Deactivator {
    /**
     * Handle plugin deactivation
     * Only drops the custom database table
     */
    public static function deactivate() {
        try {
            global $wpdb;
            

            
            // Drop the custom table - this is the only action we're keeping
            $table_name = $wpdb->prefix . 'linking_table';
            $result = $wpdb->query("DROP TABLE IF EXISTS $table_name");
            

        } catch (Exception $e) {

        }
    }
}