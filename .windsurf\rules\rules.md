---
trigger: manual
---

Rule 1:
We are building a plugin named custom-linking-plugin. All details about the plugin’s purpose, logic, and usage will be documented in a file named "instructions.md" located in the root directory.

Rule 2:
While developing, always proceed with caution. Make changes in a way that does not break existing functionality. Every change should be incremental, reversible, and tested.

Rule 3:
The plugin folder structure will contain a maximum of 4 folders:
/custom-linking-plugin/
  ├── custom-linking-plugin.php
  ├── database/
  ├── includes/
  ├── admin/
  └── frontend/
admin/ → handles all admin panel operations

database/ → contains all database-related operations

frontend/ → contains code related to frontend tasks

includes/ → contains core logic files shared across plugin

Rule 4:
Each folder will contain a file named foldernameLink.php which links all the files inside that folder. These link files will be included in the main plugin file.

Example:
/admin/adminLink.php
/includes/includesLink.php

Rule 5:
The plugin will use an autoloader class named class-core.php which will handle loading classes automatically.

Rule 6:
Code must be clean, readable, and well-commented.
Use proper indentation and spacing — make your logic self-explanatory.

Rule 7:
Stick to basics. Avoid over-engineering or complex structures.
Use simple, reliable approaches that are easy to debug and maintain.

Rule 8:
Database:
- The plugin will use a custom table named 'wp_linking_table' to store course-product relationships.
- All database operations should be properly escaped and use WordPress database functions.

Rule 9:
Debugging:
- A debug.log file will be created in the plugin's root directory for logging purposes.
- All debug output should be properly formatted and include timestamps.

Rule 10:
Development Process:
- Before implementing any feature, create a test script to verify the functionality.
- Only after successful testing should the feature be integrated into the main plugin.
- Each feature should be tested in isolation before integration.

