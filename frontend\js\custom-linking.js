"use strict";

/**
 * Custom Linking Plugin - Frontend JS
 *
 * This JavaScript file modifies the MasterStudy LMS "Get Course" button functionality
 * It keeps the original functionality intact while adding our custom override
 * for redirecting to linked WooCommerce products
 */
(function ($) {
  /**
   * This block runs immediately to ensure our handlers are attached before MasterStudy LMS
   * This is crucial for intercepting the click before their handlers
   */
  // Debug information - look for target elements
  console.log('Custom Linking Plugin: Initializing and looking for course buttons...');
  
  // Our handler for the GET COURSE button
  function interceptBuyButtonClick(e) {
    // Stop the event from propagating to MasterStudy's handlers
    e.preventDefault();
    e.stopImmediatePropagation();
    
    // Get course ID from URL or data attribute
    var courseId = $(this).closest('[data-purchased-course]').attr('data-purchased-course');
    if (!courseId) {
      // Try to get from URL
      var urlParts = window.location.pathname.split('/');
      var courseSlug = null;
      for (var i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'courses') {
          courseSlug = urlParts[i+1];
          break;
        }
      }
      courseId = courseSlug || 'unknown';
    }
    
    console.log('Custom Linking Plugin: Intercepted click for course: ' + courseId);

    // Make AJAX call to query course-product link
    $.ajax({
      url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
      type: 'POST',
      data: {
        action: 'custom_linking_course_request',
        course_id: courseId,
        nonce: customLinkingData.nonce || ''
      },
      success: function(response) {
        console.log('Custom Linking Plugin: AJAX response:', response);

        // Handle successful response
        if (response.success && response.data) {
          if (response.data.redirect_to === 'cart' && response.data.cart_url) {
            // Redirect to WooCommerce cart
            console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
            window.location.href = response.data.cart_url;
          } else if (response.data.redirect_to === 'product' && response.data.product_url) {
            // Redirect to product page
            console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
            window.location.href = response.data.product_url;
          } else {
            console.log('Custom Linking Plugin: No redirect needed or no linked product found');
          }
        }
      },
      error: function(xhr, status, error) {
        console.log('Custom Linking Plugin: AJAX error:', error);
      }
    });
    
    return false;
  }
  
  // Attach our handler BEFORE document.ready to ensure it runs first
  $(document).on('click', '.masterstudy-buy-button__link', interceptBuyButtonClick);
  console.log('Custom Linking Plugin: Button click handler attached');
  
  $(document).ready(function () {
    /**
     * This block handles subscription button styling
     * Original MasterStudy LMS code - keep intact
     */
    $('.stm_lms_mixed_button.subscription_enabled > .btn').on('click', function () {
      var height_list = $('.stm_lms_mixed_button__list')[0].clientHeight + 40;
      var sticky_buttons = $('.stm-lms-buy-buttons-sticky');
      var sticky_buy_buttons = $('.stm-lms-buy-buttons-mixed');

      if (sticky_buttons.length === 0) {
        sticky_buy_buttons.addClass('stm-lms-buy-buttons-sticky');
        sticky_buy_buttons.css('margin-bottom', height_list + 'px');
        $('.stm-lms-buy-buttons-enterprise').css('margin-bottom', '-165px');
      } else {
        sticky_buy_buttons.css('margin-bottom', '15px');
        $('.stm-lms-buy-buttons-enterprise').css('margin-bottom', '15px');
        sticky_buy_buttons.removeClass('stm-lms-buy-buttons-sticky');
      }

      $('.stm_lms_mixed_button').toggleClass('active');
    });
    
    /**
     * This block handles click outside detection
     * Original MasterStudy LMS code - keep intact
     */
    var $body = $('body');
    var buy_buttons = '.stm-lms-buy-buttons';
    $body.click(function (e) {
      if (!$(buy_buttons).is(e.target) && $(buy_buttons).has(e.target).length === 0 && !$('.stm_lms_course_sticky_panel__button').is(e.target) && $('.stm_lms_course_sticky_panel__button').has(e.target).length === 0) {
        // if div is not target nor its descendant
        $('.stm-lms-buy-buttons-sticky').css('margin-bottom', '15px');
        $(buy_buttons).removeClass('stm-lms-buy-buttons-sticky');
        $('.stm_lms_mixed_button').removeClass('active');
      }
    });
    
    /**
     * CUSTOM OVERRIDE: Get Course button click handlers
     * This is the main override functionality that intercepts clicks on the "Get Course" button
     * We use multiple selectors to ensure we catch all possible button variations
     */
    // Add debug information to see what buttons are available
    console.log('Custom Linking Plugin: Looking for course buttons...');
    console.log('GET COURSE button found:', $('.get_course_button').length);
    console.log('stm_lms_buy_button found:', $('#stm_lms_buy_button').length);
    console.log('GET COURSE text buttons found:', $('a:contains("GET COURSE")').length);
    
    // Log all buttons with their classes for debugging
    $('.stm-lms-buy-buttons .btn').each(function() {
      console.log('Found button:', $(this).text(), 'Classes:', $(this).attr('class'));
    });
    
    // Target by ID - Main buy button
    $body.on('click', '#stm_lms_buy_button', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      console.log('Custom Linking Plugin: Buy button clicked by ID!');
      
      // Get course ID from data attribute or from URL
      var courseId = $(this).data('buy-course');
      if (!courseId) {
        // Try to extract from URL if not in data attribute
        var urlParts = window.location.pathname.split('/');
        for (var i = 0; i < urlParts.length; i++) {
          if (urlParts[i] === 'course' && i+1 < urlParts.length) {
            courseId = urlParts[i+1];
            break;
          }
        }
      }
      
      // Log for debugging
      console.log('Custom Linking Plugin: Intercepted click on course ID ' + courseId);
      
      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              console.log('Custom Linking Plugin: No redirect needed or no linked product found');
            }
          }
        },
        error: function(xhr, status, error) {
          console.log('Custom Linking Plugin: AJAX error:', error);
        }
      });
      
      return false;
    });
    
    // Target by button text - Generic GET COURSE button
    $body.on('click', '.get_course_button, .btn-default:contains("GET COURSE")', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      console.log('Custom Linking Plugin: GET COURSE button clicked!');
      
      // Extract course ID from URL
      var courseId = null;
      var urlParts = window.location.pathname.split('/');
      for (var i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'course' && i+1 < urlParts.length) {
          courseId = urlParts[i+1];
          break;
        }
      }
      
      // If we couldn't find the course ID in URL, try data attributes
      if (!courseId && $(this).data('course-id')) {
        courseId = $(this).data('course-id');
      }
      
      console.log('Custom Linking Plugin: Found course ID: ' + courseId);

      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              console.log('Custom Linking Plugin: No redirect needed or no linked product found');
            }
          }
        },
        error: function(xhr, status, error) {
          console.log('Custom Linking Plugin: AJAX error:', error);
        }
      });
      
      return false;
    });
    
    // Most generic catch-all selector for any GET COURSE button
    $body.on('click', '.stm-lms-buy-buttons .btn', function(e) {
      console.log('Custom Linking Plugin: Generic button clicked!');
      e.preventDefault();
      
      // Extract course ID from URL
      var courseId = null;
      var urlParts = window.location.pathname.split('/');
      for (var i = 0; i < urlParts.length; i++) {
        if (urlParts[i] === 'course' && i+1 < urlParts.length) {
          courseId = urlParts[i+1];
          break;
        }
      }
      
      console.log('Custom Linking Plugin: Found course ID from URL: ' + courseId);

      // Make AJAX call to query course-product link
      $.ajax({
        url: customLinkingData.ajax_url || '/wp-admin/admin-ajax.php',
        type: 'POST',
        data: {
          action: 'custom_linking_course_request',
          course_id: courseId,
          nonce: customLinkingData.nonce || ''
        },
        success: function(response) {
          console.log('Custom Linking Plugin: AJAX response:', response);

          // Handle successful response
          if (response.success && response.data) {
            if (response.data.redirect_to === 'cart' && response.data.cart_url) {
              // Redirect to WooCommerce cart
              console.log('Custom Linking Plugin: Redirecting to cart:', response.data.cart_url);
              window.location.href = response.data.cart_url;
            } else if (response.data.redirect_to === 'product' && response.data.product_url) {
              // Redirect to product page
              console.log('Custom Linking Plugin: Redirecting to product page:', response.data.product_url);
              window.location.href = response.data.product_url;
            } else {
              console.log('Custom Linking Plugin: No redirect needed or no linked product found');
            }
          }
        },
        error: function(xhr, status, error) {
          console.log('Custom Linking Plugin: AJAX error:', error);
        }
      });
      
      return false;
    });

    /**
     * Guest checkout handler - original LMS code kept for reference
     * We're intercepting the main buy button above, so this code is still here
     * but will likely not be triggered for logged-in users
     */
    $body.on('click', '[data-guest]', function (e) {
      e.preventDefault();
      var item_id = $(this).data('guest');
      var currentCart = $.cookie('stm_lms_notauth_cart');
      currentCart = typeof currentCart === 'undefined' ? [] : JSON.parse(currentCart);
      if (!currentCart.includes(item_id)) currentCart.push(item_id);
      $.cookie('stm_lms_notauth_cart', JSON.stringify(currentCart), {
        path: '/'
      });
      $.ajax({
        url: stm_lms_ajaxurl,
        dataType: 'json',
        context: this,
        data: {
          item_id: item_id,
          action: 'stm_lms_add_to_cart_guest',
          nonce: stm_lms_nonces['stm_lms_add_to_cart_guest']
        },
        beforeSend: function beforeSend() {
          $(this).addClass('loading');
        },
        complete: function complete(data) {
          data = data['responseJSON'];
          $(this).removeClass('loading');
          $(this).find('span').text(data['text']);

          if (data['cart_url']) {
            if (data['redirect']) window.location = data['cart_url'];
            $(this).attr('href', data['cart_url']).removeAttr('data-guest').addClass('goToCartUrl');
          }
        }
      });
    });
    
    /**
     * Cart URL click handler - original LMS code
     */
    $body.on('click', '.goToCartUrl', function () {
      window.location.href = $(this).attr('href');
    });
    
    // Log for debugging
    console.log('Custom Linking Plugin: Frontend JS initialized');
  });
})(jQuery);
