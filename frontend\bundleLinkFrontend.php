<?php
/**
 * Bundle Linking Frontend initialization
 * 
 * This file initializes the bundle linking functionality for the Custom Linking Plugin
 * It loads scripts, registers AJAX handlers, and sets up the necessary hooks
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Include the bundle linking handler
 * This file contains the AJAX processing functions for bundle purchases
 */
require_once plugin_dir_path(__FILE__) . 'bundleLinking.php';

/**
 * Include the bundle order completion handler
 * This file contains functions for granting access after bundle purchase
 */
require_once plugin_dir_path(__FILE__) . 'bundle-completion.php';

/**
 * Enqueue bundle linking scripts and localize data
 * 
 * This function loads our JavaScript file and passes necessary data to it
 * It's hooked to the wp_enqueue_scripts action
 */
function custom_linking_enqueue_bundle_scripts() {
    /**
     * Load our custom JavaScript file that handles bundle button interception
     * We set dependencies on jQuery to ensure it's loaded first
     */
    wp_enqueue_script(
        'custom-bundle-linking-js',
        plugin_dir_url(__FILE__) . 'js/custom-bundle-linking.js',
        array('jquery'),
        CUSTOM_LINKING_VERSION,
        true
    );
    
    /**
     * Pass necessary data to our JavaScript file
     * This includes AJAX URL, nonce for security, and any configuration options
     */
    wp_localize_script(
        'custom-bundle-linking-js',
        'customBundleLinkingData',
        array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('custom_linking_nonce'),
            'debug'    => defined('CUSTOM_LINKING_DEBUG') && CUSTOM_LINKING_DEBUG,
        )
    );
}
add_action('wp_enqueue_scripts', 'custom_linking_enqueue_bundle_scripts');

/**
 * Register additional hooks for WooCommerce order completion
 * 
 * These hooks ensure we process bundle purchases when an order is completed
 * They integrate with the existing order completion system
 */
function custom_linking_init_bundle_hooks() {
    /**
     * This is a placeholder for future hook registrations
     * We'll need to integrate with order-completion.php to handle bundles
     */
}
add_action('init', 'custom_linking_init_bundle_hooks');
