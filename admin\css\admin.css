/**
 * Custom Linking Plugin Admin Styles
 * 
 * This file contains styles for the admin interface of the Custom Linking Plugin
 * It styles the main container, form elements, and table layout
 */

/* Main container layout */
.custom-linking-admin-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
    gap: 30px;
}

/**
 * Admin form styling
 * Handles the form for adding new course-product links
 * with proper spacing and control styling
 */
.custom-linking-admin-form {
    flex: 0 0 350px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.custom-linking-admin-form h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.custom-linking-admin-form .form-group {
    margin-bottom: 15px;
}

.custom-linking-admin-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.custom-linking-admin-form select {
    width: 100%;
}

/**
 * Admin list table styling
 * Styles the table displaying existing course-product links
 * with proper spacing and header styling
 */
.custom-linking-admin-list {
    flex: 1 1 600px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.custom-linking-admin-list h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/**
 * Responsive design adjustments
 * Makes the layout responsive for different screen sizes
 */
@media screen and (max-width: 782px) {
    .custom-linking-admin-container {
        flex-direction: column;
    }
    
    .custom-linking-admin-form,
    .custom-linking-admin-list {
        flex: 1 1 100%;
    }
}
