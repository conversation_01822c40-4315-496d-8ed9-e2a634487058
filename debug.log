[2025-06-06 08:36:52] Processing bundle ID: 49741
[2025-06-06 08:36:52] Found linked product: 49750 for bundle: 49741
[2025-06-06 08:36:52] Found linked product ID: 49750 for bundle: 49741
[2025-06-06 08:36:52] Product ID 49750 purchasable status: true
[2025-06-06 08:36:52] Checking for cart conflicts before adding bundle product 49750
[2025-06-06 08:36:52] Manual bundle course configuration: Bundle 49741 contains courses Array
(
    [0] => 49599
)

[2025-06-06 08:36:52] Bundle 49741 contains courses: Array
(
    [0] => 49599
)

[2025-06-06 08:36:52] Successfully added product ID: 49750 to cart (key: 7bbab236560bb6828cf4b88203a63ae6)
[2025-06-06 08:37:42] Processing order 49757 for user 29
[2025-06-06 08:37:42] Processing product 49750 from order 49757
[2025-06-06 08:37:42] Found linked course 49741 (type: bundle) for product 49750
[2025-06-06 08:37:42] ATTEMPTING TO GRANT: Course 49741 access to user 29 (type: bundle)
[2025-06-06 08:37:42] Starting course access grant for user 29, course 49741, type: bundle
[2025-06-06 08:37:42] BUNDLE DETECTED: Processing bundle 49741 for user 29
[2025-06-06 08:37:42] Bundle processing: link_type = bundle, course_id = 49741
[2025-06-06 08:37:42] Bundle completion function already available
[2025-06-06 08:37:42] Calling custom_linking_grant_bundle_access for user 29, bundle 49741
[2025-06-06 08:37:42] Granting bundle access: Bundle #49741 for User #29
[2025-06-06 08:37:42] Using Course Bundler's native enrollment system for bundle #49741
[2025-06-06 08:37:42] Recorded bundle enrollment in Course Bundler system for user #29 in bundle #49741
[2025-06-06 08:37:42] SUCCESS: Bundle access granted for bundle 49741 to user 29
[2025-06-06 08:37:42] SUCCESS: Granted access to course 49741 for user 29
[2025-06-06 08:37:42] Processing product 49729 from order 49757
[2025-06-06 08:37:42] Found linked course 49599 (type: course) for product 49729
[2025-06-06 08:37:42] ATTEMPTING TO GRANT: Course 49599 access to user 29 (type: course)
[2025-06-06 08:37:42] Starting course access grant for user 29, course 49599, type: course
[2025-06-06 08:37:42] Processing regular course access for course 49599
[2025-06-06 08:37:42] Found MasterStudy LMS user_courses table: wp_stm_lms_user_courses
[2025-06-06 08:37:42] Method 1: Successfully enrolled user 29 in course 49599 via user_courses table
[2025-06-06 08:37:42] SUCCESS: Granted access to course 49599 for user 29
[2025-06-06 08:37:42] SUCCESS: Granted access to course 49599 for user 29
[2025-06-06 08:37:42] Processing product 461 from order 49757
[2025-06-06 08:37:42] No linked courses found for product 461
[2025-06-06 08:37:42] Completed processing order 49757
