<?php
/**
 * Plugin Name: Custom Linking Plugin
 * Plugin URI: https://yourwebsite.com/custom-linking-plugin
 * Description: Links MasterStudy LMS courses to WooCommerce products
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * Text Domain: custom-linking-plugin
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.2
 */

/**
 * Direct debug log function to be available immediately
 * before any includes are loaded
 */
if (!function_exists('custom_linking_direct_log')) {
    /**
     * Simple direct logging function for debugging activation issues
     * This function does not depend on any other code or includes
     * 
     * @param string $message Message to log
     */
    function custom_linking_direct_log($message) {
        $plugin_dir = dirname(__FILE__);
        $log_file = $plugin_dir . '/debug.log';
        
        // Create timestamp
        $timestamp = date('[Y-m-d H:i:s]');
        
        // Format message
        if (is_array($message) || is_object($message)) {
            $message = print_r($message, true);
        }
        
        // Create log message
        $log_message = $timestamp . ' ' . $message . "\n";
        
        // Write to file using direct method
        $handle = @fopen($log_file, 'a');
        if ($handle) {
            @fwrite($handle, $log_message);
            @fclose($handle);
        }
        
        // Also write to PHP error log as backup
        error_log('Custom Linking Plugin: ' . $message);
    }
}

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
if (!defined('CUSTOM_LINKING_PLUGIN_VERSION')) {
    define('CUSTOM_LINKING_PLUGIN_VERSION', '1.0.0');
}

if (!defined('CUSTOM_LINKING_PLUGIN_FILE')) {
    define('CUSTOM_LINKING_PLUGIN_FILE', __FILE__);
}

if (!defined('CUSTOM_LINKING_PLUGIN_PATH')) {
    define('CUSTOM_LINKING_PLUGIN_PATH', dirname(__FILE__) . '/');
}

if (!defined('CUSTOM_LINKING_PLUGIN_URL')) {
    // Calculate the URL based on the plugin directory structure
    $plugin_dir = basename(dirname(__FILE__));
    // Use WordPress functions if available, otherwise use basic structure
    if (function_exists('plugin_dir_url')) {
        define('CUSTOM_LINKING_PLUGIN_URL', plugin_dir_url(__FILE__));
    } else {
        // Fallback for when WordPress functions aren't available yet
        define('CUSTOM_LINKING_PLUGIN_URL', '/wp-content/plugins/' . $plugin_dir . '/');
    }
}

/**
 * Load only essential files during plugin scanning
 * Everything else will be loaded when WordPress is ready
 */

/**
 * Returns the main instance of the plugin
 * This function will be called only when WordPress is ready
 *
 * @return Custom_Linking_Core The main plugin instance
 */
function custom_linking_plugin() {
    // Load debug utilities
    if (file_exists(dirname(__FILE__) . '/includes/debug.php')) {
        require_once dirname(__FILE__) . '/includes/debug.php';

    }

    // Include all required files
    $plugin_path = CUSTOM_LINKING_PLUGIN_PATH;

    // Include core files
    if (file_exists($plugin_path . 'includes/linkIncludes.php')) {
        require_once $plugin_path . 'includes/linkIncludes.php';

    }

    // Include database files
    if (file_exists($plugin_path . 'database/linkDatabase.php')) {
        require_once $plugin_path . 'database/linkDatabase.php';

    }

    // Include admin files
    if (file_exists($plugin_path . 'admin/linkAdmin.php')) {
        require_once $plugin_path . 'admin/linkAdmin.php';

    }

    // Include frontend files
    if (file_exists($plugin_path . 'frontend/linkFrontend.php')) {
        require_once $plugin_path . 'frontend/linkFrontend.php';

    }
    
    // Include bundle linking frontend files
    if (file_exists($plugin_path . 'frontend/bundleLinkFrontend.php')) {
        require_once $plugin_path . 'frontend/bundleLinkFrontend.php';

    }

    // Include the main plugin class
    if (!class_exists('Custom_Linking_Core')) {
        require_once CUSTOM_LINKING_PLUGIN_PATH . 'class-core.php';

    }


    return Custom_Linking_Core::instance();
}

/**
 * Register activation and deactivation hooks directly in main file
 * This is more reliable than registering them in the class
 * Only register if we're in a WordPress environment (ABSPATH is defined)
 */
if (defined('ABSPATH') && function_exists('register_activation_hook') && function_exists('register_deactivation_hook')) {
    register_activation_hook(__FILE__, 'custom_linking_activate');
    register_deactivation_hook(__FILE__, 'custom_linking_deactivate');
}

/**
 * Activation function
 */
function custom_linking_activate() {
    try {
        // Create the database table - check if file exists first
        $activator_file = dirname(__FILE__) . '/database/Activator.php';

        if (file_exists($activator_file)) {
            require_once $activator_file;

            if (class_exists('Custom_Linking_Activator')) {
                Custom_Linking_Activator::activate();
            }
        }
    } catch (Exception $e) {
        // Re-throw to help WordPress display the error
        throw $e;
    }
}

/**
 * Deactivation function
 */
function custom_linking_deactivate() {
    try {
        // Drop the database table - check if file exists first
        $deactivator_file = dirname(__FILE__) . '/database/Deactivator.php';

        if (file_exists($deactivator_file)) {
            require_once $deactivator_file;

            if (class_exists('Custom_Linking_Deactivator')) {
                Custom_Linking_Deactivator::deactivate();
            }
        }
    } catch (Exception $e) {
        // Silently handle deactivation errors
    }
}

/**
 * Initialize the plugin when WordPress is ready
 */
function custom_linking_plugin_init() {
    // Load the main plugin class
    $GLOBALS['custom_linking_plugin'] = custom_linking_plugin();
}

// Initialize the plugin - check if WordPress functions are available
if (defined('ABSPATH') && function_exists('add_action')) {
    add_action('plugins_loaded', 'custom_linking_plugin_init');
}
