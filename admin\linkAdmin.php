<?php
/**
 * Admin functionality for Custom Linking Plugin
 *
 * This file links all admin-related functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Include admin-related files
 */
require_once dirname(__FILE__) . '/admin.php';

/**
 * Admin functions for WordPress admin pages and scripts
 * 
 * These functions are hooked in the core class
 * to ensure proper loading order
 */

/**
 * Register the admin menu and page
 */
function custom_linking_admin_menu() {
    add_menu_page(
        __('Course-Product Linking', 'custom-linking-plugin'),
        __('Linking Plugin', 'custom-linking-plugin'),
        'manage_options',
        'custom-linking-plugin',
        'custom_linking_admin_page',
        'dashicons-admin-links',
        61 // Between Appearance (60) and Plugins (70)
    );
}

/**
 * Enqueue admin scripts and styles
 */
function custom_linking_admin_scripts($hook) {
    // Only load on our admin page
    if ($hook != 'toplevel_page_custom-linking-plugin') {
        return;
    }
    
    // Enqueue CSS
    wp_enqueue_style(
        'custom-linking-admin-styles',
        CUSTOM_LINKING_PLUGIN_URL . 'admin/css/admin.css',
        array(),
        CUSTOM_LINKING_PLUGIN_VERSION,
        'all'
    );

    // Enqueue JavaScript
    wp_enqueue_script(
        'custom-linking-admin-scripts',
        CUSTOM_LINKING_PLUGIN_URL . 'admin/js/admin.js',
        array('jquery'),
        CUSTOM_LINKING_PLUGIN_VERSION,
        true
    );
    
    // Add localized script data
    wp_localize_script(
        'custom-linking-admin-scripts',
        'custom_linking_data',
        array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('custom-linking-nonce'),
            'messages' => array(
                'success' => __('Link saved successfully', 'custom-linking-plugin'),
                'error' => __('Error saving link', 'custom-linking-plugin'),
                'delete_confirm' => __('Are you sure you want to delete this link?', 'custom-linking-plugin')
            )
        )
    );
}