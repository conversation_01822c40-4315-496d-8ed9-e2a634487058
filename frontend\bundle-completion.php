<?php
/**
 * Bundle Order Completion Handler for Custom Linking Plugin
 * 
 * This file handles WooCommerce order completion events for bundle purchases
 * and automatically grants access to all courses in the bundle
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Include debug utilities
require_once dirname(dirname(__FILE__)) . '/includes/debug.php';

/**
 * Process completed bundle purchases and grant course access
 *
 * This function is called after WooCommerce order completion
 * It checks for linked bundles and grants access to all courses within them
 *
 * @param int $order_id The WooCommerce order ID
 * @param int $product_id The product ID that was purchased
 * @param int $user_id The user who made the purchase
 */
function custom_linking_process_bundle_purchase($order_id, $product_id, $user_id) {
    /**
     * This block logs the function call for debugging purposes
     * It records the order, product, and user IDs to track the process
     */
    custom_linking_debug_log("Processing possible bundle purchase: Order #{$order_id}, Product #{$product_id}, User #{$user_id}");
    
    /**
     * Find the bundle ID linked to this product
     * We query our custom linking table to find the bundle association
     */
    $bundle_id = custom_linking_get_bundle_by_product($product_id);
    
    /**
     * If a bundle is found, grant access to all courses in it
     * Otherwise, this might be a regular product, not a bundle
     */
    if ($bundle_id) {
        custom_linking_debug_log("Found bundle #{$bundle_id} linked to product #{$product_id}");
        
        // Grant access to all courses in the bundle
        $result = custom_linking_grant_bundle_access($user_id, $bundle_id);
        
        if ($result) {
            custom_linking_debug_log("Successfully granted access to all courses in bundle #{$bundle_id} for user #{$user_id}");
            return true;
        } else {
            custom_linking_debug_log("Failed to grant access to some or all courses in bundle #{$bundle_id}");
            return false;
        }
    } else {
        custom_linking_debug_log("No bundle found for product #{$product_id}");
        return false; // Not a bundle product
    }
}

/**
 * Helper function to get bundle ID linked to a product
 *
 * @param int $product_id The WooCommerce product ID
 * @return int|false The linked bundle ID or false if none found
 */
function custom_linking_get_bundle_by_product($product_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'linking_table';
    
    $bundle_id = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT course_id FROM $table_name WHERE product_id = %d AND type = %s LIMIT 1",
            $product_id,
            'bundle'
        )
    );
    
    return $bundle_id ? (int) $bundle_id : false;
}

/**
 * Helper function to grant access to all courses in a bundle
 * 
 * This function directly uses the Course Bundler's enrollment system
 * to enroll the user in all courses within a bundle
 *
 * @param int $user_id The WordPress user ID
 * @param int $bundle_id The bundle ID
 * @return bool True if access was granted to all courses successfully
 */
function custom_linking_grant_bundle_access($user_id, $bundle_id) {
    /**
     * This block logs the function call for debugging purposes
     * It records which bundle is being processed for which user
     */
    custom_linking_debug_log("Granting bundle access: Bundle #{$bundle_id} for User #{$user_id}");
    
    /**
     * Check if the Course Bundler enrollment class exists
     * This is the preferred method as it uses the bundler's native enrollment system
     */
    if (class_exists('MSCB_Enrollment')) {
        // Create an instance of the enrollment class
        $enrollment = new MSCB_Enrollment();
        
        // Use reflection to access the private method
        $reflection = new ReflectionClass('MSCB_Enrollment');
        $method = $reflection->getMethod('enroll_user_in_bundle');
        $method->setAccessible(true);
        
        // Call the private method to enroll user
        custom_linking_debug_log("Using Course Bundler's native enrollment system for bundle #{$bundle_id}");
        $method->invokeArgs($enrollment, array($user_id, $bundle_id));
        
        // Record bundle completion in the bundler's system too
        if (class_exists('MSCB_Bundle')) {
            $bundle = new MSCB_Bundle();
            $bundle->enroll_user($bundle_id, $user_id);
            custom_linking_debug_log("Recorded bundle enrollment in Course Bundler system for user #{$user_id} in bundle #{$bundle_id}");
        }
        
        return true;
    }
    
    /**
     * Fallback method: manually enroll in each course
     * Only used if the Course Bundler enrollment class isn't available
     */
    custom_linking_debug_log("Fallback: Manual enrollment for bundle #{$bundle_id}");

    // Try to get courses from Course Bundler plugin if available
    $courses = array();

    if (class_exists('MSCB_Bundle')) {
        custom_linking_debug_log("MSCB_Bundle class found, attempting to get bundle courses");
        $bundle = new MSCB_Bundle();
        $courses = $bundle->get_bundle_courses($bundle_id);
    } else {
        custom_linking_debug_log("MSCB_Bundle class not found, trying alternative methods to get bundle courses");

        // Alternative method 1: Check if bundle is a MasterStudy LMS course with bundle meta
        $bundle_courses_meta = get_post_meta($bundle_id, 'stm_lms_bundle_courses', true);
        if (!empty($bundle_courses_meta) && is_array($bundle_courses_meta)) {
            custom_linking_debug_log("Found bundle courses in post meta: " . print_r($bundle_courses_meta, true));
            foreach ($bundle_courses_meta as $course_id) {
                $courses[] = array('course_id' => $course_id);
            }
        }

        // Alternative method 2: Check for custom bundle structure in database
        if (empty($courses)) {
            global $wpdb;
            $bundle_courses_table = $wpdb->prefix . 'stm_lms_bundle_courses';
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$bundle_courses_table}'");

            if ($table_exists) {
                custom_linking_debug_log("Found bundle courses table, querying for bundle #{$bundle_id}");
                $course_results = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT course_id FROM {$bundle_courses_table} WHERE bundle_id = %d",
                        $bundle_id
                    )
                );

                if (!empty($course_results)) {
                    foreach ($course_results as $course_result) {
                        $courses[] = array('course_id' => $course_result->course_id);
                    }
                    custom_linking_debug_log("Found " . count($courses) . " courses in bundle table");
                }
            }
        }

        // Alternative method 3: Manual course list (for testing/fallback)
        if (empty($courses)) {
            custom_linking_debug_log("No bundle courses found via automatic methods, checking for manual configuration");

            // For testing purposes, you can manually define bundle courses here
            // This should be replaced with proper bundle configuration
            if ($bundle_id == 49741) {
                // Example: Bundle 49741 contains these courses (replace with actual course IDs)
                // Based on the debug log, we know course 49599 should be in this bundle
                $manual_courses = array(49599); // Add more course IDs as needed
                foreach ($manual_courses as $course_id) {
                    $courses[] = array('course_id' => $course_id);
                }
                custom_linking_debug_log("Using manual course configuration for bundle #{$bundle_id}: " . print_r($manual_courses, true));
            }

            // Add more bundle configurations as needed
            // if ($bundle_id == ANOTHER_BUNDLE_ID) {
            //     $manual_courses = array(COURSE_ID_1, COURSE_ID_2, COURSE_ID_3);
            //     foreach ($manual_courses as $course_id) {
            //         $courses[] = array('course_id' => $course_id);
            //     }
            //     custom_linking_debug_log("Using manual course configuration for bundle #{$bundle_id}");
            // }
        }
    }

    if (empty($courses)) {
        custom_linking_debug_log("No courses found in bundle #{$bundle_id} after trying all methods");
        return false;
    }

    custom_linking_debug_log("Found " . count($courses) . " courses in bundle #{$bundle_id}");
    
    // Process each course using the native STM_LMS_Course method
    $success = true;
    foreach ($courses as $course) {
        $course_id = $course['course_id'];
        
        if (class_exists('STM_LMS_Course')) {
            // Use the same method as the Course Bundler does
            STM_LMS_Course::add_user_course(
                $course_id,
                $user_id,
                0, // current_lesson_id
                0, // progress
                false, // is_translate
                '', // enterprise
                $bundle_id, // bundle_id
                '', // for_points
                '' // instructor_id
            );
            
            // Add student count like the Course Bundler does
            STM_LMS_Course::add_student($course_id);
            
            custom_linking_debug_log("Enrolled user #{$user_id} in course #{$course_id} from bundle #{$bundle_id}");
        } else {
            // Ultimate fallback to our custom enrollment
            if (function_exists('custom_linking_grant_course_access')) {
                $result = custom_linking_grant_course_access($user_id, $course_id, 'bundle');
                if (!$result) {
                    custom_linking_debug_log("Failed to grant access to course #{$course_id} from bundle #{$bundle_id}");
                    $success = false;
                }
            } else {
                custom_linking_debug_log("Error: Cannot find any enrollment function for course #{$course_id}");
                $success = false;
            }
        }
    }
    
    // Also record the enrollment in the bundle system if available
    if ($success && class_exists('MSCB_Bundle')) {
        $bundle->enroll_user($bundle_id, $user_id);
    }
    
    return $success;
}

/**
 * Hook into WooCommerce order processing
 * This attaches our bundle processor to the order completion pipeline
 */
function custom_linking_init_bundle_purchase_hook() {
    // Hook into the custom linking order processing
    add_action('custom_linking_process_product_purchase', 'custom_linking_process_bundle_purchase', 10, 3);
}
add_action('init', 'custom_linking_init_bundle_purchase_hook');
